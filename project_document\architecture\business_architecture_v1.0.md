# 业务架构文档 v1.3

**版本历史:**
- v1.3 (2025-06-04 10:43:20 +08:00): AR (齐天大圣) - 简化用户角色，明确G部门核心地位及基于部门权限的场景细化。
- v1.2 (2025-06-04 10:19:34 +08:00): AR (齐天大圣) - 增加主管部门直接发起/下达信息、及直接上报信息至领导层的流程。强调操作记录。
- v1.1 (2025-06-04 10:00:21 +08:00): AR (齐天大圣) - 根据用户反馈修订信息上报下达流程及数据可见性。
- v1.0 (2025-06-04 09:43:27 +08:00): AR (齐天大圣) - 初版，基于单体架构方案。

## 1. 项目概述
本项目旨在开发一款用于集团内部信息上报与下达的桌面及Web应用。初期采用单体架构，后端使用GoFrame，桌面端使用Wails，Web端使用Vue+Naive UI。系统需包含完整的RBAC功能。数据库明确使用SQLite。

## 2. 用户角色与权限体系
- **用户角色:** 系统仅设两类基础角色：
    - **普通用户:** 集团内所有部门的常规操作人员。
    - **系统管理员:** 拥有系统最高配置和管理权限。
- **部门权限:** 用户的具体操作能力（如审核、指派、处理特定信息）将基于其所属部门以及分配给其角色的针对特定部门或业务范围的细粒度权限来控制。例如，"信息主管部门G"的普通用户可能被授予"审核所有提交至G的信息"、"向下级部门指派信息"等权限。

## 3. 核心部门定义
- **信息主管部门 (G):** 核心协调部门，负责监督、审核、指派多数跨部门信息，并对特定信息有最终处置权。
- **其他业务部门 (A, B, C等):** 信息的发起方或处理方。

## 4. 核心业务流程
1.  **用户管理:** (系统管理员操作用户创建、角色分配)
2.  **角色管理:** (系统管理员定义"普通用户"、"系统管理员"角色)
3.  **权限管理:** (系统管理员定义细粒度权限，如 `report:initiate`, `report:review_dept_G`, `report:assign_from_G`, `report:process_dept_A`, `report:close_dept_G`, `report:escalate_dept_G`)
4.  **资源管理:** (菜单、API)
5.  **部门管理:** (系统管理员创建部门，可指定某用户为部门负责人——此"负责人"不代表特定角色，但其用户账号在G部门时可能被授予G部门特有权限)

6.  **信息处理总流程:**
    *   **6.1. 信息发起:**
        *   **场景一 (G部门直接发起并指派):** 信息主管部门G的用户（凭相应权限）直接在系统中创建信息，并指派给其他业务部门A处理。
            *   *操作记录: G部门发起并指派至A*
        *   **场景二 (B部门发起内部处理):** B部门用户（凭权限）在系统中录入信息。若判定为B部门内部可闭环处理事务，则B部门内部直接处理。
            *   *操作记录: B部门发起内部信息*
        *   **场景三 (B部门发起，G部门审核后指派):** B部门用户在系统中录入信息（特别是可能涉及跨部门或需G部门监督类型）。信息流转至G部门审核。
            *   *操作记录: B部门提交信息待G审核*
    *   **6.2. G部门审核与指派 (针对场景三类型信息):**
        *   G部门用户（凭权限）审核来自其他部门（如B）的提交。
            *   **审核通过并指派:** 指派给目标处理部门A。
                *   *操作记录: G审核通过并指派至A*
            *   **审核驳回:** 退回给发起部门B或关闭。
                *   *操作记录: G审核驳回*
    *   **6.3. 信息处理与进展更新:**
        *   被指派的处理部门（如A或B内部处理）的用户（凭权限）更新信息处理进展。
            *   *操作记录: 处理进展更新 (部门A/B)*
    *   **6.4. 信息关闭:**
        *   **场景一&三的关闭:** 处理部门A完成处理后，可标记"请求关闭"。G部门用户（凭权限）审核处理结果并确认关闭信息，形成闭环。
            *   *操作记录: A请求关闭, G确认关闭*
        *   **场景二的关闭:** B部门处理内部事务后，若B本身即为G部门，则可直接关闭。若B非G部门，其关闭内部事务的权限由系统配置决定（例如，某些类型信息B可自行关闭，其他仍需G最终确认，或G拥有对所有信息的最终关闭权）。（*简化处理：除非明确B=G，否则非G部门发起的内部信息，若需正式闭环，也建议由G确认或留痕*）。G部门用户也可直接关闭其发起或监管的信息。
            *   *操作记录: B处理完毕/请求关闭, G确认关闭/直接关闭*
    *   **6.5. 主管部门标记上报至领导层 (特殊操作):**
        *   G部门用户（凭权限）可将任何流转中或已处理的关键信息标记为"已上报至集团领导"。这主要是一个状态和审计记录，不一定改变当前处理流程。
            *   *操作记录: G标记信息上报领导层*
    *   **强调：** 所有关键操作均需记录（操作人、部门、时间、类型、备注）。

7.  **信息发布 (通知公告):** (流程保持v1.2不变，发布权限同样基于部门权限)

## 5. 数据可见性
(基于角色和部门权限进行控制)
-   **普通用户 (非G部门):**
    *   查看自己发起的信息及完整处理过程。
    *   查看指派给自己部门且自己有权处理的信息。
    *   查看对其部门发布的通知公告。
-   **普通用户 (G部门成员，根据权限细分):**
    *   可查看G部门发起、G部门审核、G部门处理或G部门关闭的所有信息。
    *   根据权限可操作审核、指派、关闭、标记上报等。
-   **系统管理员:** 完全数据可见性。

## 6. 关键业务实体 (修订后 v1.3)
- 用户 (User)
- 角色 (Role: e.g. `ROLE_USER`, `ROLE_SYSTEM_ADMIN`)
- 权限 (Permission: e.g. `report:initiate_self_dept`, `report:initiate_G_to_other_dept`, `report:review_G`, `report:assign_G`, `report:process_assigned`, `report:request_close_assigned`, `report:confirm_close_G`, `report:escalate_G`)
- 菜单 (Menu)
- API接口 (ApiEndpoint)
- 部门 (Department)
- 信息记录 (Report)
    - 发起类型 (InitiatorType: `USER_SUBMITTED_FOR_G_REVIEW`, `DEPT_G_INITIATED_ASSIGN`, `DEPT_SELF_INITIATED_INTERNAL`)
    - 当前状态 (Status: e.g., `PENDING_G_REVIEW`, `PENDING_DEPT_ASSIGNMENT` (by G), `ASSIGNED_TO_DEPT_X`, `DEPT_X_PROCESSING`, `PENDING_G_CLOSURE_APPROVAL`, `ESCALATED_TO_LEADERSHIP`, `CLOSED_RESOLVED`, `CLOSED_REJECTED`)
    - (其他字段如 initiator_id, initiator_dept_id, current_handler_dept_ids, is_escalated_to_leadership 保持或微调)
- 信息处理过程 (ReportProcess)
    - 操作类型 (ActionType: e.g., `USER_SUBMIT_FOR_G`, `G_INITIATE_ASSIGN`, `G_REVIEW_PASS_ASSIGN`, `G_REVIEW_REJECT`, `DEPT_PROCESS_UPDATE`, `DEPT_REQUEST_CLOSE`, `G_CONFIRM_CLOSE`, `G_DIRECT_CLOSE`, `G_ESCALATE`)
- 通知公告 (Notification) 