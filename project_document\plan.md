# 项目实施计划 v1.0

**版本历史:**
- v1.0 (2025-06-04 09:43:27 +08:00): PM (齐天大圣) - 初版，基于单体架构方案。

## 1. 项目概述
开发一款基于GoFrame的单体应用，提供信息上报与下达功能，包含完整的RBAC权限管理。桌面端使用Wails，Web端使用Vue+NaiveUI。

## 2. 总体计划阶段
1.  **P1: 环境搭建与基础框架 (LD)**
2.  **P2: RBAC核心功能实现 (LD, AR)**
3.  **P3: 业务功能模块 - 信息上报与下达 (LD)**
4.  **P4: Web前端实现 (LD/UI/UX)**
5.  **P5: Wails桌面端实现 (LD)**
6.  **P6: 测试与集成 (TE, LD)**
7.  **P7: 文档完善与部署准备 (DW, AR)**

## 3. 详细任务分解

---
### 阶段 P1: 环境搭建与基础框架
---
**任务 P1-LD-001**
- **描述：** 初始化GoFrame项目，配置基础环境。
- **负责人角色 (概念上):** LD
- **技术栈/语言：** GoFrame, Golang
- **关键步骤：**
    1. 安装GoFrame CLI。
    2. `gf init` 创建项目。
    3. 配置数据库连接 (初期可使用SQLite或内存)。
    4. 配置日志、配置文件读取。
    5. 搭建基础项目结构 (参照 `code_architecture_v1.0.md`)。
- **验收标准：** 项目能成功启动，可访问一个健康检查接口。
- **测试策略：** 运行项目，手动测试健康检查接口。
- **预估工时 (概念上)：** 4小时
- **依赖任务：** 无
- **时间戳 (计划制定):** 2025-06-04 09:43:27 +08:00

**任务 P1-LD-002**
- **描述：** 集成Wails环境到GoFrame项目（或建立关联）。
- **负责人角色 (概念上):** LD
- **技术栈/语言：** Wails, Golang
- **关键步骤：**
    1. 安装Wails CLI。
    2. 初始化Wails项目。
    3. 研究Wails与GoFrame后端（作为独立API服务）的集成方式。
    4. 确保Wails前端可以调用GoFrame的API。
- **验收标准：** Wails应用能启动，并能成功调用GoFrame的测试API。
- **测试策略：** 运行Wails应用，测试API调用。
- **预估工时 (概念上)：** 6小时
- **依赖任务：** P1-LD-001
- **时间戳 (计划制定):** 2025-06-04 09:43:27 +08:00

**任务 P1-LD-003**
- **描述：** 初始化Vue + Naive UI前端项目。
- **负责人角色 (概念上):** LD (兼顾前端初期搭建)
- **技术栈/语言：** Vue.js, Naive UI, Vite/Webpack
- **关键步骤：**
    1. 使用Vite或Vue CLI创建Vue项目。
    2. 集成Naive UI。
    3. 配置API请求模块 (如axios) 指向GoFrame后端。
    4. 搭建基本布局和路由。
- **验收标准：** Vue项目能启动，基本界面可访问，可调用GoFrame测试API。
- **测试策略：** 运行Vue项目，手动测试。
- **预估工时 (概念上)：** 6小时
- **依赖任务：** P1-LD-001
- **时间戳 (计划制定):** 2025-06-04 09:43:27 +08:00

---
### 阶段 P2: RBAC核心功能实现 (后端)
---
(参考 `data_structures_v1.0.md` 和 `api_specs_v1.0.md`)

**任务 P2-AR-001 (设计确认)**
- **描述：** 确认RBAC相关数据表结构。
- **负责人角色 (概念上):** AR
- **关键步骤：**
    1. 评审 `data_structures_v1.0.md` 中的用户、角色、权限、用户角色、角色权限表。
    2. 根据GoFrame特性生成DAO、Entity代码 (`gf gen dao`)。
- **验收标准：** 数据表结构无异议，DAO代码生成成功。
- **测试策略：** 代码审查。
- **预估工时 (概念上)：** 2小时
- **依赖任务：** P1-LD-001
- **时间戳 (计划制定):** 2025-06-04 09:43:27 +08:00

**任务 P2-LD-002**
- **描述：** 实现用户认证模块 (登录、登出、获取用户信息)。
- **负责人角色 (概念上):** LD
- **技术栈/语言：** GoFrame
- **关键步骤：**
    1. 实现登录接口逻辑 (密码校验、Token生成 - JWT)。
    2. 实现登出接口逻辑 (Token失效处理)。
    3. 实现获取当前用户信息接口。
    4. 编写中间件进行Token验证和权限解析。
- **验收标准：** API接口符合 `api_specs_v1.0.md` 定义，单元测试通过。
- **测试策略：** 单元测试，API接口测试 (Postman/gf client)。
- **预估工时 (概念上)：** 8小时
- **依赖任务：** P2-AR-001
- **时间戳 (计划制定):** 2025-06-04 09:43:27 +08:00

**任务 P2-LD-003**
- **描述：** 实现用户管理模块API (CRUD)。
- **负责人角色 (概念上):** LD
- **技术栈/语言：** GoFrame
- **关键步骤：** 用户增删改查接口。
- **验收标准：** API接口符合 `api_specs_v1.0.md` 定义，单元测试通过。
- **测试策略：** 单元测试，API接口测试。
- **预估工时 (概念上)：** 6小时
- **依赖任务：** P2-AR-001
- **时间戳 (计划制定):** 2025-06-04 09:43:27 +08:00

**任务 P2-LD-004**
- **描述：** 实现角色管理模块API (CRUD, 角色权限分配)。
- **负责人角色 (概念上):** LD
- **技术栈/语言：** GoFrame
- **关键步骤：** 角色增删改查，为角色分配/移除权限接口。
- **验收标准：** API接口符合 `api_specs_v1.0.md` 定义，单元测试通过。
- **测试策略：** 单元测试，API接口测试。
- **预估工时 (概念上)：** 8小时
- **依赖任务：** P2-AR-001
- **时间戳 (计划制定):** 2025-06-04 09:43:27 +08:00

**任务 P2-LD-005**
- **描述：** 实现权限/菜单管理模块API (CRUD, 树形结构)。
- **负责人角色 (概念上):** LD
- **技术栈/语言：** GoFrame
- **关键步骤：** 权限/菜单增删改查，返回树形结构数据接口。
- **验收标准：** API接口符合 `api_specs_v1.0.md` 定义，单元测试通过。
- **测试策略：** 单元测试，API接口测试。
- **预估工时 (概念上)：** 8小时
- **依赖任务：** P2-AR-001
- **时间戳 (计划制定):** 2025-06-04 09:43:27 +08:00

**任务 P2-LD-006**
- **描述：** 实现RBAC权限校验逻辑 (API层面)。
- **负责人角色 (概念上):** LD, AR
- **技术栈/语言：** GoFrame
- **关键步骤：**
    1. 设计权限校验中间件或服务。
    2. 能根据用户角色获取其权限列表。
    3. 校验用户是否有权访问特定API或执行操作。
    4. GoFrame推荐使用 `gf security` 或casbin等库的集成。
- **验收标准：** 受保护的API在无权限时返回403，有权限时正常访问。
- **测试策略：** 单元测试，集成测试不同用户角色的API访问。
- **预估工时 (概念上)：** 10小时
- **依赖任务：** P2-LD-002, P2-LD-004, P2-LD-005
- **时间戳 (计划制定):** 2025-06-04 09:43:27 +08:00

---
### 阶段 P3: 业务功能模块实现 (后端)
---
(参考 `data_structures_v1.0.md` 和 `api_specs_v1.0.md`)

**任务 P3-LD-001**
- **描述：** 实现信息上报模块API (CRUD, 审批流程)。
- **负责人角色 (概念上):** LD
- **技术栈/语言：** GoFrame
- **验收标准：** API接口符合定义，单元测试通过。
- **测试策略：** 单元测试，API接口测试。
- **预估工时 (概念上)：** 10小时
- **依赖任务：** P2-LD-006 (依赖用户和权限)
- **时间戳 (计划制定):** 2025-06-04 09:43:27 +08:00

**任务 P3-LD-002**
- **描述：** 实现信息下达/通知公告模块API (CRUD, 发布)。
- **负责人角色 (概念上):** LD
- **技术栈/语言：** GoFrame
- **验收标准：** API接口符合定义，单元测试通过。
- **测试策略：** 单元测试，API接口测试。
- **预估工时 (概念上)：** 8小时
- **依赖任务：** P2-LD-006
- **时间戳 (计划制定):** 2025-06-04 09:43:27 +08:00

---
### 阶段 P4: Web前端实现 (Vue + Naive UI)
---
**任务 P4-LD-001 (兼前端)**
- **描述：** 实现登录页面及认证逻辑。
- **负责人角色 (概念上):** LD/UI/UX
- **技术栈/语言：** Vue, Naive UI
- **验收标准：** 登录成功后跳转到主界面，Token存入localStorage/sessionStorage。
- **测试策略：** 手动测试，E2E测试 (P4-TE-XXX)。
- **预估工时 (概念上)：** 8小时
- **依赖任务：** P1-LD-003, P2-LD-002 (后端API)
- **时间戳 (计划制定):** 2025-06-04 09:43:27 +08:00

**任务 P4-LD-002 (兼前端)**
- **描述：** 实现整体应用布局 (导航栏、侧边栏菜单、内容区)。
- **负责人角色 (概念上):** LD/UI/UX
- **技术栈/语言：** Vue, Naive UI
- **关键步骤：**
    1. 根据用户权限动态生成侧边栏菜单。
    2. 实现路由守卫进行页面访问权限控制。
- **验收标准：** 界面布局符合设计，菜单根据权限动态展示。
- **测试策略：** 手动测试。
- **预估工时 (概念上)：** 10小时
- **依赖任务：** P4-LD-001, P2-LD-005 (后端API获取菜单)
- **时间戳 (计划制定):** 2025-06-04 09:43:27 +08:00

**任务 P4-LD-003 (兼前端)**
- **描述：** 实现用户管理页面 (CRUD)。
- **负责人角色 (概念上):** LD/UI/UX
- **技术栈/语言：** Vue, Naive UI
- **验收标准：** 功能与后端API对接正常。
- **测试策略：** 手动测试。
- **预估工时 (概念上)：** 8小时
- **依赖任务：** P4-LD-002, P2-LD-003 (后端API)
- **时间戳 (计划制定):** 2025-06-04 09:43:27 +08:00

**任务 P4-LD-004 (兼前端)**
- **描述：** 实现角色管理页面 (CRUD, 权限分配 - 通常使用穿梭框或树形选择器)。
- **负责人角色 (概念上):** LD/UI/UX
- **技术栈/语言：** Vue, Naive UI
- **验收标准：** 功能与后端API对接正常。
- **测试策略：** 手动测试。
- **预估工时 (概念上)：** 10小时
- **依赖任务：** P4-LD-002, P2-LD-004 (后端API)
- **时间戳 (计划制定):** 2025-06-04 09:43:27 +08:00

**任务 P4-LD-005 (兼前端)**
- **描述：** 实现权限/菜单管理页面 (CRUD, 树形展示)。
- **负责人角色 (概念上):** LD/UI/UX
- **技术栈/语言：** Vue, Naive UI
- **验收标准：** 功能与后端API对接正常。
- **测试策略：** 手动测试。
- **预估工时 (概念上)：** 8小时
- **依赖任务：** P4-LD-002, P2-LD-005 (后端API)
- **时间戳 (计划制定):** 2025-06-04 09:43:27 +08:00

**任务 P4-LD-006 (兼前端)**
- **描述：** 实现信息上报页面 (表单提交、列表查看、审批操作)。
- **负责人角色 (概念上):** LD/UI/UX
- **技术栈/语言：** Vue, Naive UI
- **验收标准：** 功能与后端API对接正常。
- **测试策略：** 手动测试。
- **预估工时 (概念上)：** 12小时
- **依赖任务：** P4-LD-002, P3-LD-001 (后端API)
- **时间戳 (计划制定):** 2025-06-04 09:43:27 +08:00

**任务 P4-LD-007 (兼前端)**
- **描述：** 实现通知公告页面 (发布、列表查看)。
- **负责人角色 (概念上):** LD/UI/UX
- **技术栈/语言：** Vue, Naive UI
- **验收标准：** 功能与后端API对接正常。
- **测试策略：** 手动测试。
- **预估工时 (概念上)：** 10小时
- **依赖任务：** P4-LD-002, P3-LD-002 (后端API)
- **时间戳 (计划制定):** 2025-06-04 09:43:27 +08:00

---
### 阶段 P5: Wails桌面端实现
---
(Wails前端将复用Web前端的Vue组件和逻辑，主要工作是Wails的壳子和与Go方法的绑定)

**任务 P5-LD-001**
- **描述：** 配置Wails项目，使其加载Vue前端构建产物。
- **负责人角色 (概念上):** LD
- **技术栈/语言：** Wails, Golang, Vue
- **关键步骤：**
    1. 将Vue项目构建后的静态文件 (HTML, CSS, JS) 集成到Wails。
    2. Wails应用启动时加载Vue应用。
- **验收标准：** Wails桌面应用能完整显示Web前端界面。
- **测试策略：** 启动Wails应用，手动验证。
- **预估工时 (概念上)：** 6小时
- **依赖任务：** P1-LD-002, (所有P4阶段任务完成后的构建产物)
- **时间戳 (计划制定):** 2025-06-04 09:43:27 +08:00

**任务 P5-LD-002**
- **描述：** 实现Wails与GoFrame后端的安全通信。
- **负责人角色 (概念上):** LD
- **技术栈/语言：** Wails, Golang
- **关键步骤：**
    1. 确保Wails内嵌的浏览器能正确发送带有认证信息的API请求。
    2. 处理可能出现的CORS问题或配置代理。
- **验收标准：** Wails应用内功能与Web端一致，API调用正常。
- **测试策略：** 手动测试Wails应用所有功能。
- **预估工时 (概念上)：** 4小时
- **依赖任务：** P5-LD-001
- **时间戳 (计划制定):** 2025-06-04 09:43:27 +08:00

**任务 P5-LD-003**
- **描述：** Wails应用打包与分发测试。
- **负责人角色 (概念上):** LD
- **技术栈/语言：** Wails
- **验收标准：** 生成不同平台的安装包，可在目标系统成功安装运行。
- **测试策略：** 在Windows, macOS, Linux上测试安装包。
- **预估工时 (概念上)：** 4小时
- **依赖任务：** P5-LD-002
- **时间戳 (计划制定):** 2025-06-04 09:43:27 +08:00

---
### 阶段 P6: 测试与集成
---
**任务 P6-TE-001**
- **描述：** 编写后端核心模块单元测试。
- **负责人角色 (概念上):** TE, LD
- **验收标准：** 核心业务逻辑单元测试覆盖率达到预期。
- **测试策略：** 使用Go testing框架。
- **预估工时 (概念上)：** (贯穿P2, P3阶段)
- **时间戳 (计划制定):** 2025-06-04 09:43:27 +08:00

**任务 P6-TE-002**
- **描述：** 进行API集成测试。
- **负责人角色 (概念上):** TE
- **验收标准：** 所有API接口按预期工作。
- **测试策略：** 使用Postman或编写自动化API测试脚本。
- **预估工时 (概念上)：** 16小时
- **依赖任务：** P2, P3阶段完成
- **时间戳 (计划制定):** 2025-06-04 09:43:27 +08:00

**任务 P6-TE-003**
- **描述：** Web前端功能测试。
- **负责人角色 (概念上):** TE
- **验收标准：** Web端所有功能符合需求。
- **测试策略：** 手动测试，编写E2E测试脚本 (Playwright)。
- **预估工时 (概念上)：** 20小时
- **依赖任务：** P4阶段完成
- **时间戳 (计划制定):** 2025-06-04 09:43:27 +08:00
    * **子任务 P6-TE-003a (Playwright):** 登录登出E2E测试
    * **子任务 P6-TE-003b (Playwright):** RBAC功能E2E测试
    * **子任务 P6-TE-003c (Playwright):** 核心业务流程E2E测试

**任务 P6-TE-004**
- **描述：** Wails桌面端功能测试。
- **负责人角色 (概念上):** TE
- **验收标准：** 桌面端所有功能符合需求，与Web端表现一致。
- **测试策略：** 手动测试。
- **预估工时 (概念上)：** 12小时
- **依赖任务：** P5阶段完成
- **时间戳 (计划制定):** 2025-06-04 09:43:27 +08:00

---
### 阶段 P7: 文档完善与部署准备
---
**任务 P7-DW-001**
- **描述：** 完善用户手册和部署文档。
- **负责人角色 (概念上):** DW, AR
- **验收标准：** 文档清晰、准确、完整。
- **预估工时 (概念上)：** 8小时
- **时间戳 (计划制定):** 2025-06-04 09:43:27 +08:00

**任务 P7-AR-002**
- **描述：** 准备单机部署包和云端部署方案 (初步)。
- **负责人角色 (概念上):** AR, LD
- **验收标准：** 提供可执行的部署步骤。
- **预估工时 (概念上)：** 6小时
- **时间戳 (计划制定):** 2025-06-04 09:43:27 +08:00

## 4. 风险与应对
- **需求变更:** 保持与用户沟通，小步快跑，及时调整。
- **技术难点:** 预留研究时间，团队协作攻关。
- **Wails与Vue集成问题:** P1阶段重点攻克。

(此计划为初步规划，具体任务和工时会根据实际进展调整。)
