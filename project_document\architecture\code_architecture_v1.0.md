# 代码架构文档 v1.3

**版本历史:**
- v1.3 (2025-06-04 10:43:20 +08:00): AR (齐天大圣) - RBAC模块描述调整为基于部门权限。
- v1.2 (部分修订，内部草稿状态)
- v1.1 (2025-06-04 10:00:21 +08:00): AR (齐天大圣) - 明确SQLite，更新GoFrame v2.x目录结构风格，增加Department模块。
- v1.0 (2025-06-04 09:43:27 +08:00): AR (齐天大圣) - 初版，单体架构，GoFrame, Wails, Vue+NaiveUI。

## 1. 概述
本文档定义项目代码层面的架构设计。采用单体架构，遵循GoFrame推荐的项目结构和设计模式。明确使用SQLite作为数据库。

## 2. 技术栈
- 后端: GoFrame v2.x
- 前端 (Web): Vue 3 + Naive UI + Vite
- 前端 (Desktop): Wails v2 (Go + Vue 3)
- 数据库: SQLite
- 版本控制: Git

## 3. 应用分层 (GoFrame典型分层)
- **router (路由层):** 定义API路由，主要进行请求的注册和初步的参数绑定和校验，以及服务注册。
- **api (接口层):** 定义对外暴露的API接口结构体（请求与响应）。
- **controller (控制层):** 接收和校验来自路由层的输入，调用service层处理业务逻辑，并返回响应。
- **service (服务层):** 实现核心业务逻辑，不直接操作数据库，而是通过dao层。
- **dao (数据访问层):** 负责数据库的直接操作（CRUD），封装数据表实体。
- **logic (业务封装层，可选):** 对于一些可复用的复杂业务逻辑单元，可以在service层之下、dao层之上增加logic层，供多个service调用。
- **internal/packed (资源打包):** Wails和Web静态资源打包。
- **internal/consts (常量定义):** 项目中使用的常量。
- **internal/model (模型定义):**
    - `entity`: 与数据库表结构一一对应的Go结构体，由DAO自动生成或手写。
    - `do`: Data Object，用于DAO层操作的结构体，通常与`entity`相似。
    - `dto`: Data Transfer Object，用于service层输入输出的结构体，由api层转换而来或直接使用。
- **utility/utils (工具类):** 提供通用工具函数。

## 4. GoFrame推荐项目结构 (v2.x 风格, 已采纳)
```
/my-app
├── main.go                 # 程序入口
├──go.mod
├──hack                     # 工具脚本等
├──manifest                 # 部署清单、配置文件模板
│   ├──config
│   │   └──config.yaml      # 主配置文件
│   └──docker               # Dockerfile等
├──internal
│   ├──cmd                  # 命令行管理(gf-cli)
│   │   └──cmd.go
│   ├──consts               # 常量定义
│   ├──controller           # 控制器(handler)
│   │   └──hello
│   │       └──hello.go
│   ├──dao                  # 数据访问对象(与表对应)
│   │   ├──internal         # 内部DAO, 通常由工具生成
│   │   │   └──user.go
│   │   └──user.go          # 对外暴露的DAO
│   ├──logic                # 业务逻辑封装(可选)
│   ├──middleware           # 中间件
│   │   └──response.go
│   ├──model                # 数据结构(input/output/entity/do)
│   │   ├──do
│   │   │   └──user.go
│   │   ├──dto              # Data Transfer Object for service
│   │   │   └──user.go
│   │   └──entity
│   │       └──user.go
│   ├──packed               # 资源打包(gf pack)
│   ├──router               # 路由注册
│   │   └──router.go
│   └──service              # 业务逻辑服务
│       └──user.go
└──api
    └──hello
        └──v1
            └──hello.go     # API接口定义 (请求与响应结构体)
```

## 5. 核心模块划分 (v1.3)
- **认证模块 (Auth):**
    - 用户登录、登出。
    - Token生成与校验中间件。
- **RBAC模块 (权限控制):**
    - **角色管理:** 系统管理员管理"普通用户"和"系统管理员"角色。
    - **权限管理:** 系统管理员定义细粒度权限项 (e.g., `report:initiate_G_to_other_dept`, `report:review_G`)。这些权限的设计将直接映射到`business_architecture_v1.3.md`中定义的业务操作和部门职责。
    - **用户-角色分配:** 系统管理员为用户分配基础角色。
    - **角色-权限分配:** 系统管理员为角色分配权限。
    - **权限校验中间件:** 核心组件。根据用户token解析其角色和所属部门，再结合请求的资源（API路径、方法），查询该角色是否拥有操作此资源的权限。
        - **部门权限实现关键点:** 对于如审核、指派等操作，权限校验逻辑需进一步结合信息本身的属性（如当前状态、发起部门、处理部门）和当前用户的部门信息。例如，一个定义为`report:review_G_dept_submissions`的权限，会校验用户是否属于G部门，并且操作目标信息是否适合由G部门审核。
- **用户模块 (User):**
    - 用户信息管理（增删改查，主要由系统管理员操作）。
- **部门模块 (Department):**
    - 部门信息管理（增删改查，主要由系统管理员操作）。
- **信息记录模块 (Report):**
    - 核心业务，实现`business_architecture_v1.3.md`中定义的各种信息处理场景：
        - G部门直接发起并指派。
        - B部门发起内部处理。
        - B部门发起，G部门审核后指派。
        - G部门审核与指派。
        - 各部门处理与进展更新。
        - 信息关闭（G部门确认或特定条件下自闭环）。
        - G部门标记上报至领导层。
    - 完整的操作记录/审计追踪功能，记录在`report_processes`表中。
- **通知公告模块 (Notification):**
    - 通知发布、查看。权限控制同样基于用户的部门和分配的权限。
- **系统设置模块 (System):**
    - (初期可简化，后续可包含日志查看、系统参数配置等)

## 6. 数据库设计
参考 `./project_document/architecture/data_structures_v1.0.md` (内容将更新至v1.3)。数据库使用SQLite。

## 7. API设计
参考 `./project_document/architecture/api_specs_v1.0.md` (内容将更新至v1.3)。遵循RESTful风格，使用JSON进行数据交换。

## 8. 前后端交互
- Web端 (Vue) 通过HTTP(S)调用GoFrame后端API。
- Wails桌面端通过其内部机制调用Go方法（这些Go方法内部再调用service层，确保逻辑复用，与HTTP API殊途同归）。

## 9. 代码规范与质量
- 遵循Go社区编码规范 (gofmt, golangci-lint 或 staticcheck)。
- GoFrame推荐的最佳实践。
- 关键业务逻辑编写单元测试。
- 遵循KISS, YAGNI, DRY等核心编程原则。
- 代码注释清晰，重要逻辑需要说明。 