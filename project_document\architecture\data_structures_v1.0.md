# 数据结构文档 v1.3

**版本历史:**
- v1.3 (2025-06-04 10:43:20 +08:00): AR (齐天大圣) - 简化角色，细化权限、报告状态和处理动作以匹配新业务场景。
- v1.2 (2025-06-04 10:19:34 +08:00): AR (齐天大圣) - 扩展reports表支持部门发起和上报领导层；扩展report_processes支持新操作类型。
- v1.1 (2025-06-04 10:00:21 +08:00): AR (齐天大圣) - 根据用户反馈修订，精简字段，调整表结构以支持新流程，明确SQLite。
- v1.0 (2025-06-04 09:43:27 +08:00): AR (齐天大圣) - 初版。

## 1. 概述
本文档定义核心业务实体的数据表结构，数据库使用 **SQLite**。字段类型以SQLite兼容类型表示。所有表默认包含 `id` (主键), `created_at`, `updated_at`。软删除 (`deleted_at`) 按需添加。

## 2. 表结构

### 2.1. 用户表 (`users`)
| 字段名        | 类型          | 约束/备注                     |
|---------------|---------------|-------------------------------|
| `id`          | `INTEGER`     | `PRIMARY KEY AUTOINCREMENT`   |
| `uuid`        | `TEXT`        | `UNIQUE`, `NOT NULL`          |
| `username`    | `TEXT`        | `UNIQUE`, `NOT NULL`          |
| `password`    | `TEXT`        | `NOT NULL` (加密)           |
| `nickname`    | `TEXT`        |                               |
| `department_id`| `INTEGER`    | `FK to departments.id`        |
| `status`      | `INTEGER`     | `DEFAULT 1` (1:启用, 0:禁用)  |
| `created_at`  | `DATETIME`    | `DEFAULT CURRENT_TIMESTAMP`   |
| `updated_at`  | `DATETIME`    | `DEFAULT CURRENT_TIMESTAMP`   |

### 2.2. 角色表 (`roles`)
| 字段名        | 类型          | 约束/备注                     |
|---------------|---------------|-------------------------------|
| `id`          | `INTEGER`     | `PRIMARY KEY AUTOINCREMENT`   |
| `name`        | `TEXT`        | `UNIQUE`, `NOT NULL` (e.g., "普通用户", "系统管理员")      |
| `key`         | `TEXT`        | `UNIQUE`, `NOT NULL` (e.g., `user`, `system_admin`) |
| `description` | `TEXT`        |                               |
| `created_at`  | `DATETIME`    | `DEFAULT CURRENT_TIMESTAMP`   |
| `updated_at`  | `DATETIME`    | `DEFAULT CURRENT_TIMESTAMP`   |

### 2.3. 权限表 (`permissions`) (菜单/操作/API)
| 字段名        | 类型          | 约束/备注                                     |
|---------------|---------------|-----------------------------------------------|
| `id`          | `INTEGER`     | `PRIMARY KEY AUTOINCREMENT`                   |
| `name`        | `TEXT`        | `NOT NULL` (e.g., "G部门发起并指派信息", "审核G部门信息") |
| `key`         | `TEXT`        | `UNIQUE`, `NOT NULL` (e.g., `report:G_initiate_assign`, `report:G_review`, `report:A_process_update`, `report:G_confirm_close`) | 
| `type`        | `TEXT`        | `NOT NULL` ('menu', 'button', 'api')          |
| `parent_id`   | `INTEGER`     | `DEFAULT 0`                                   |
| `path`        | `TEXT`        | (前端路由/API路径)                            |
| `component`   | `TEXT`        | (前端组件路径)                                |
| `icon`        | `TEXT`        |                                               |
| `order_num`   | `INTEGER`     | `DEFAULT 0`                                   |
| `created_at`  | `DATETIME`    | `DEFAULT CURRENT_TIMESTAMP`                   |
| `updated_at`  | `DATETIME`    | `DEFAULT CURRENT_TIMESTAMP`                   |

### 2.4. 用户-角色关联表 (`user_roles`)
| 字段名     | 类型     | 约束/备注                                 |
|------------|----------|-------------------------------------------|
| `user_id`  | `INTEGER`| `NOT NULL`, `FK to users.id`              |
| `role_id`  | `INTEGER`| `NOT NULL`, `FK to roles.id`              |
| `created_at`| `DATETIME`| `DEFAULT CURRENT_TIMESTAMP`               |
| PRIMARY KEY (`user_id`, `role_id`)                               |

### 2.5. 角色-权限关联表 (`role_permissions`)
| 字段名        | 类型     | 约束/备注                                     |
|---------------|----------|-----------------------------------------------|
| `role_id`     | `INTEGER`| `NOT NULL`, `FK to roles.id`                  |
| `permission_id`| `INTEGER`| `NOT NULL`, `FK to permissions.id`            |
| `created_at`  | `DATETIME`| `DEFAULT CURRENT_TIMESTAMP`                   |
| PRIMARY KEY (`role_id`, `permission_id`)                                 |

### 2.6. 部门表 (`departments`)
| 字段名        | 类型          | 约束/备注                     |
|---------------|---------------|-------------------------------|
| `id`          | `INTEGER`     | `PRIMARY KEY AUTOINCREMENT`   |
| `name`        | `TEXT`        | `UNIQUE`, `NOT NULL`          |
| `parent_id`   | `INTEGER`     | `DEFAULT 0`                   |
| `manager_id`  | `INTEGER`     | `NULLABLE`, `FK to users.id` (部门名义负责人) | 
| `order_num`   | `INTEGER`     | `DEFAULT 0`                   |
| `created_at`  | `DATETIME`    | `DEFAULT CURRENT_TIMESTAMP`   |
| `updated_at`  | `DATETIME`    | `DEFAULT CURRENT_TIMESTAMP`   |

### 2.7. 信息记录表 (`reports`)
| 字段名                     | 类型        | 约束/备注                                     |
|----------------------------|-------------|-----------------------------------------------|
| `id`                       | `INTEGER`   | `PRIMARY KEY AUTOINCREMENT`                   |
| `title`                    | `TEXT`      | `NOT NULL`                                    |
| `content`                  | `TEXT`      | `NOT NULL`                                    |
| `initiator_id`             | `INTEGER`   | `NOT NULL`, `FK to users.id` (发起人)        |
| `initiator_dept_id`        | `INTEGER`   | `NOT NULL`, `FK to departments.id` (发起人当时所在部门) |
| `initiator_type`           | `TEXT`      | `NOT NULL` (e.g., 'SCENARIO_G_INITIATE_ASSIGN', 'SCENARIO_B_INTERNAL', 'SCENARIO_B_SUBMIT_FOR_G_REVIEW') | 
| `status`                   | `TEXT`      | `NOT NULL` (e.g., 'PENDING_G_REVIEW' (B submitted), 'ASSIGNED_TO_A_BY_G', 'DEPT_A_PROCESSING', 'DEPT_B_PROCESSING_INTERNAL', 'PENDING_G_CLOSURE_CONFIRM_FROM_A', 'ESCALATED_TO_LEADERSHIP_BY_G', 'CLOSED_BY_G', 'CLOSED_REJECTED_BY_G', 'CLOSED_BY_B_INTERNAL' (if permitted)) | 
| `is_escalated_to_leadership`| `INTEGER`  | `DEFAULT 0` (0: false, 1: true)               |
| `current_handler_dept_ids` | `TEXT`      | `NULLABLE` (JSON array of department IDs, e.g., `[dept_A_id]`)     |
| `created_at`               | `DATETIME`  | `DEFAULT CURRENT_TIMESTAMP`                   |
| `updated_at`               | `DATETIME`  | `DEFAULT CURRENT_TIMESTAMP`                   |
| `closed_at`                | `DATETIME`  | `NULLABLE`                                    |

### 2.8. 信息处理过程表 (`report_processes`)
(此表用于完整的操作审计记录)
| 字段名            | 类型        | 约束/备注                                     |
|-------------------|-------------|-----------------------------------------------|
| `id`              | `INTEGER`   | `PRIMARY KEY AUTOINCREMENT`                   |
| `report_id`       | `INTEGER`   | `NOT NULL`, `FK to reports.id`                |
| `operator_id`     | `INTEGER`   | `NOT NULL`, `FK to users.id` (操作人)        |
| `operator_dept_id`| `INTEGER`   | `NOT NULL`, `FK to departments.id` (操作人当时部门) |
| `action_type`     | `TEXT`      | `NOT NULL` (e.g., 'G_INITIATE_ASSIGN_TO_A', 'B_INITIATE_INTERNAL', 'B_SUBMIT_FOR_G_REVIEW', 'G_REVIEW_PASS_ASSIGN_TO_A', 'G_REVIEW_REJECT', 'A_PROCESS_UPDATE', 'B_PROCESS_UPDATE_INTERNAL', 'A_REQUEST_CLOSURE_TO_G', 'G_CONFIRM_CLOSURE_FROM_A', 'G_DIRECT_CLOSE', 'B_REQUEST_INTERNAL_CLOSE', 'G_CONFIRM_INTERNAL_CLOSE_B' (if G involved), 'G_ESCALATE_TO_LEADERSHIP') | 
| `assigned_dept_ids`| `TEXT`     | `NULLABLE` (JSON array of department IDs, for 'G_INITIATE_ASSIGN_TO_A' or 'G_REVIEW_PASS_ASSIGN_TO_A') |
| `remarks`         | `TEXT`      | (处理意见/说明)                               |
| `created_at`      | `DATETIME`  | `DEFAULT CURRENT_TIMESTAMP`                   |

### 2.9. 通知公告表 (`notifications`)
| 字段名            | 类型        | 约束/备注                                     |
|-------------------|-------------|-----------------------------------------------|
| `id`              | `INTEGER`   | `PRIMARY KEY AUTOINCREMENT`                   |
| `title`           | `TEXT`      | `NOT NULL`                                    |
| `content`         | `TEXT`      | `NOT NULL`                                    |
| `publisher_id`    | `INTEGER`   | `NOT NULL`, `FK to users.id`                  |
| `status`          | `INTEGER`   | `DEFAULT 0` (0:草稿, 1:已发布)                 |
| `published_at`    | `DATETIME`  | `NULLABLE`                                    |
| `created_at`      | `DATETIME`  | `DEFAULT CURRENT_TIMESTAMP`                   |
| `updated_at`      | `DATETIME`  | `DEFAULT CURRENT_TIMESTAMP`                   |

### 2.10. 通知公告目标部门表 (`notification_target_departments`)
| 字段名            | 类型        | 约束/备注                                     |
|-------------------|-------------|-----------------------------------------------|
| `notification_id` | `INTEGER`   | `NOT NULL`, `FK to notifications.id`          |
| `department_id`   | `INTEGER`   | `NOT NULL`, `FK to departments.id`            |
| PRIMARY KEY (`notification_id`, `department_id`)                               |

## 3. 数据字典
(后续迭代中补充各字段详细含义、枚举值等。`status` 和 `action_type` 字段的具体枚举值需与API设计紧密对应。) 