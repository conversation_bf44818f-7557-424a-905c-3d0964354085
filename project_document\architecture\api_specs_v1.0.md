# API 规范文档 v1.1

**版本历史:**
- v1.1 (2025-06-04 10:00:21 +08:00): AR (齐天大圣) - 根据用户反馈修订业务流程，调整相关API接口。
- v1.0 (2025-06-04 09:43:27 +08:00): AR (齐天大圣) - 初版。

## 1. 概述
本文档定义后端API接口规范。所有API均通过HTTP(S)提供，遵循RESTful设计原则。
API基础路径: `/api/v1`

## 2. 通用约定
- **请求方法:** 使用标准的HTTP方法 (GET, POST, PUT, DELETE, PATCH)。
- **请求头:**
    - `Content-Type: application/json`
    - `Authorization: Bearer <token>` (登录后需要认证的接口)
- **响应格式:** JSON
    ```json
    {
        "code": 0, // 0 表示成功，其他表示错误码
        "message": "success", // 提示信息
        "data": {} // 业务数据，可能为null, object, array
    }
    ```
- **错误码:** (后续定义详细错误码表)
    - `0`: 成功
    - `401`: 未认证
    - `403`: 无权限
    - `404`: 资源未找到
    - `500`: 服务器内部错误
    - `10000+`: 业务错误码
- **分页:**
    - 请求参数: `page` (页码, default 1), `pageSize` (每页数量, default 10)
    - 响应数据中包含: `total` (总记录数), `list` (当前页数据)
      ```json
      "data": {
          "list": [...],
          "total": 100,
          "page": 1,
          "pageSize": 10
      }
      ```

## 3. API 端点 (修订后)

### 3.1. 认证 (`/auth`)
- `POST /auth/login`: 用户登录
  - 请求: `{ "username": "xxx", "password": "xxx" }`
  - 响应: `{ "token": "xxx", "userInfo": { ... } }`
- `POST /auth/logout`: 用户登出
- `GET /auth/userinfo`: 获取当前登录用户信息 (通过Token)

### 3.2. 用户管理 (`/users`)
- `POST /users`: 创建用户 (管理员)
- `GET /users`: 获取用户列表 (分页、查询)
- `GET /users/{id}`: 获取用户详情
- `PUT /users/{id}`: 更新用户信息
- `DELETE /users/{id}`: 删除用户
- `PUT /users/{id}/password`: 修改用户密码 (管理员重置或用户自己修改)

### 3.3. 角色管理 (`/roles`)
- `POST /roles`: 创建角色
- `GET /roles`: 获取角色列表 (分页、查询)
- `GET /roles/all`: 获取所有角色 (用于下拉选择)
- `GET /roles/{id}`: 获取角色详情
- `PUT /roles/{id}`: 更新角色信息
- `DELETE /roles/{id}`: 删除角色
- `GET /roles/{id}/permissions`: 获取角色拥有的权限ID列表
- `PUT /roles/{id}/permissions`:为角色分配权限 (传入权限ID列表)

### 3.4. 权限/菜单管理 (`/permissions`)
- `POST /permissions`: 创建权限/菜单
- `GET /permissions`: 获取权限/菜单列表 (树形结构)
- `GET /permissions/tree`: 获取权限菜单树 (通常用于前端渲染菜单和角色授权)
- `GET /permissions/{id}`: 获取权限/菜单详情
- `PUT /permissions/{id}`: 更新权限/菜单信息
- `DELETE /permissions/{id}`: 删除权限/菜单

### 3.5. 部门管理 (`/departments`) (新增)
- `POST /departments`: 创建部门
- `GET /departments`: 获取部门列表 (可支持树形)
- `GET /departments/{id}`: 获取部门详情
- `PUT /departments/{id}`: 更新部门信息 (含设置部门管理员)
- `DELETE /departments/{id}`: 删除部门
- `GET /departments/users`: 获取部门下的用户列表 (可选，或通过用户管理接口筛选)

### 3.6. 信息上报与处置 (`/reports`) (重大调整)
- `POST /reports`: 用户提交上报信息
  - 请求: `{ "title": "xxx", "content": "xxx" }` (submitter_id, submitter_dept_id 后端获取)
- `GET /reports`: 获取上报信息列表 (分页、查询)
  - 参数可包含: `status`, `submitted_by_me`, `assigned_to_my_dept`, `managed_by_my_dept` 等，后端根据用户权限过滤。
- `GET /reports/{id}`: 获取上报信息详情 (包含处理过程 `report_processes`)
- `POST /reports/{id}/review`: 部门管理员审核
  - 请求: `{ "action": "pass/reject", "remarks": "xxx" }`
- `POST /reports/{id}/assign`: (审核通过后)部门管理员指派给其他处理部门
  - 请求: `{ "target_dept_ids": [1, 2], "remarks": "xxx" }`
- `POST /reports/{id}/process`: 处理部门人员更新处理进展/结果
  - 请求: `{ "remarks": "xxx", "attachments": [...] }` (附件可选)
- `POST /reports/{id}/request_close`: 处理部门请求关闭该信息
  - 请求: `{ "remarks": "final solution..." }`
- `POST /reports/{id}/confirm_close`: (初始审核)部门管理员或系统管理员确认关闭
  - 请求: `{ "remarks": "confirmed" }`

### 3.7. 通知公告 (`/notifications`) (调整)
- `POST /notifications`: 创建/保存通知公告 (管理员)
  - 请求: `{ "title": "xxx", "content": "xxx", "target_dept_ids": [1,2] }` (status默认为草稿)
- `GET /notifications`: 获取通知公告列表 (分页、查询)
  - 管理员端: 可看所有、草稿、已发布
  - 用户端: 只能看已发布的、且目标包含自己部门的
- `GET /notifications/{id}`: 获取通知公告详情
- `PUT /notifications/{id}`: 更新通知公告
- `DELETE /notifications/{id}`: 删除通知公告
- `POST /notifications/{id}/publish`: 发布通知 (将草稿设为发布状态，设置 published_at)
- `POST /notifications/{id}/unpublish`: 撤销发布 (可选功能)

(API端点会根据具体实现进一步细化) 